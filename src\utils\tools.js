/**
 * 获取URL中的查询参数
 * @param {string} [url] - 需要解析的URL，默认为当前页面URL
 * @param {string} [key] - 需要获取的参数名，不传则返回所有参数对象
 * @returns {Object|string|null} - 返回查询参数对象或指定参数值，未找到指定参数时返回null
 */
export function getUrlQuery (url, key) {
  // 如果未传入URL，则使用当前页面URL
  const targetUrl = url || window.location.href;

  // 创建URL对象
  let urlObj;
  try {
    urlObj = new URL(targetUrl);
  } catch (e) {
    console.error('无效的URL:', targetUrl);
    return key ? null : {};
  }

  // 获取查询参数
  const searchParams = urlObj.searchParams;

  // 如果指定了key，则返回对应的值
  if (key) {
    const value = searchParams.get(key);
    return value === null ? null : value;
  }

  // 否则返回所有参数对象
  const paramsObj = {};
  searchParams.forEach((value, key) => {
    paramsObj[key] = value;
  });

  return paramsObj;
}

