/* 全局样式和主题变量 */
:root {
  --bg-color: #f8f9fa;
  --text-color: #333;
  --header-bg: #fff;
  --sidebar-bg: #fff;
  --card-bg: #f9f9f9;
  --toolbar-bg: #fafafa;
  --modal-bg: #fff;
  --border-color: #eee;
  --input-bg: #fff;
  --button-bg: #fff;
  --button-hover-bg: #f0f0f0;
  --primary-color: #1890ff;
  --primary-color-hover: #40a9ff;
  --success-color: #52c41a;
  --success-color-hover: #73d13d;
  --error-color: #f5222d;
  --error-bg: #fff2f0;
  --error-border: #ffccc7;
  --secondary-text: #666;
  --title-color: #222;
  --grey-color: #8c8c8c;
  --grey-color-hover: #666;
}

:root.dark-theme {
  --bg-color: #121212;
  --text-color: #e0e0e0;
  --header-bg: #1f1f1f;
  --sidebar-bg: #1f1f1f;
  --card-bg: #282828;
  --toolbar-bg: #282828;
  --modal-bg: #1f1f1f;
  --border-color: #333;
  --input-bg: #282828;
  --button-bg: #333;
  --button-hover-bg: #444;
  --primary-color: #177ddc;
  --primary-color-hover: #1890ff;
  --success-color: #49aa19;
  --success-color-hover: #52c41a;
  --error-color: #ff4d4f;
  --error-bg: #2a1215;
  --error-border: #5c2223;
  --secondary-text: #aaa;
  --title-color: #e0e0e0;
  --grey-color: #666;
  --grey-color-hover: #888;
}

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Arial', 'Helvetica', 'PingFang SC', 'Microsoft YaHei', sans-serif;
  color: var(--text-color);
  background-color: var(--bg-color);
}

* {
  box-sizing: border-box;
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-color);
}

::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #aaa;
}

:root.dark-theme ::-webkit-scrollbar-thumb {
  background: #444;
}

:root.dark-theme ::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* 按钮通用样式 */
button {
  cursor: pointer;
  border: none;
  outline: none;
}

/* 动画过渡 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 连接桩默认隐藏 */
.x6-port-body {
  visibility: hidden;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .flow-sidebar {
    width: 220px !important;
  }

  .flow-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 480px) {
  .flow-sidebar {
    position: absolute;
    z-index: 100;
    height: 100%;
  }

  .action-btn span {
    display: none;
  }

  .modal-container {
    width: 90% !important;
  }
}