/**
 * 缓存工具类
 * 提供本地存储和会话存储的增强包装
 */

/**
 * 本地存储封装
 * 用于持久化存储数据（浏览器关闭后数据仍然保留）
 */
export const localCache = {
  /**
   * 设置本地存储项
   * @param {string} key - 存储键名
   * @param {string|number} value - 存储值
   */
  set (key, value) {
    if (!key) return;
    try {
      localStorage.setItem(key, value);
    } catch (error) {
      console.error('本地存储设置失败:', error);
    }
  },

  /**
   * 获取本地存储项
   * @param {string} key - 存储键名
   * @returns {string|null} 存储值
   */
  get (key) {
    if (!key) return null;
    return localStorage.getItem(key);
  },

  /**
   * 移除本地存储项
   * @param {string} key - 存储键名
   */
  remove (key) {
    if (!key) return;
    localStorage.removeItem(key);
  },

  /**
   * 清空所有本地存储
   */
  clear () {
    localStorage.clear();
  },

  /**
   * 设置JSON数据到本地存储
   * @param {string} key - 存储键名
   * @param {Object} value - 要存储的对象
   */
  setJSON (key, value) {
    if (!key || value === undefined) return;
    try {
      const jsonValue = JSON.stringify(value);
      this.set(key, jsonValue);
    } catch (error) {
      console.error('JSON对象存储失败:', error);
    }
  },

  /**
   * 从本地存储获取JSON数据
   * @param {string} key - 存储键名
   * @returns {Object|null} 解析后的对象
   */
  getJSON (key) {
    if (!key) return null;

    try {
      const value = this.get(key);
      if (value) {
        return JSON.parse(value);
      }
    } catch (error) {
      console.error('JSON解析失败:', error);
    }

    return null;
  }
};

/**
 * 会话存储封装
 * 用于临时存储数据（浏览器关闭后数据清除）
 */
export const sessionCache = {
  /**
   * 设置会话存储项
   * @param {string} key - 存储键名
   * @param {string|number} value - 存储值
   */
  set (key, value) {
    if (!key) return;
    try {
      sessionStorage.setItem(key, value);
    } catch (error) {
      console.error('会话存储设置失败:', error);
    }
  },

  /**
   * 获取会话存储项
   * @param {string} key - 存储键名
   * @returns {string|null} 存储值
   */
  get (key) {
    if (!key) return null;
    return sessionStorage.getItem(key);
  },

  /**
   * 移除会话存储项
   * @param {string} key - 存储键名
   */
  remove (key) {
    if (!key) return;
    sessionStorage.removeItem(key);
  },

  /**
   * 清空所有会话存储
   */
  clear () {
    sessionStorage.clear();
  },

  /**
   * 设置JSON数据到会话存储
   * @param {string} key - 存储键名
   * @param {Object} value - 要存储的对象
   */
  setJSON (key, value) {
    if (!key || value === undefined) return;
    try {
      const jsonValue = JSON.stringify(value);
      this.set(key, jsonValue);
    } catch (error) {
      console.error('JSON对象存储失败:', error);
    }
  },

  /**
   * 从会话存储获取JSON数据
   * @param {string} key - 存储键名
   * @returns {Object|null} 解析后的对象
   */
  getJSON (key) {
    if (!key) return null;

    try {
      const value = this.get(key);
      if (value) {
        return JSON.parse(value);
      }
    } catch (error) {
      console.error('JSON解析失败:', error);
    }

    return null;
  }
};
