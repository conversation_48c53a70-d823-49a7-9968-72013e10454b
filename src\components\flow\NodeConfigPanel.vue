<script setup>
import { ref, watch } from 'vue';
import { useFlowGraph } from '../../composables/useFlowGraph';
import { useConfigOptions } from '../../composables/useConfigOptions';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  selectedNode: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['close', 'update']);

const { graph, updateNodeStyle, updateNodeData } = useFlowGraph();

// 获取配置选项
const { roleList } = useConfigOptions();

// 当前激活的标签页
const activeTab = ref('basic'); // 'basic' 或 'business'

// 节点样式配置
const nodeStyle = ref({
  stroke: '#5F95FF',
  strokeWidth: 2,
  fill: '#EFF7FF',
  fontSize: 14,
  fontColor: '#333333',
  labelText: '',
  width: 120,
  height: 60,
  rx: 8,
  ry: 8
});

// 业务配置数据
const businessConfig = ref({
  activityId: '', // 活动ID字段
  roleName: '',   // 角色名称
  description: '' // 备注（角色描述）
});

// 监听选中的节点变化
watch(() => props.selectedNode, (node) => {
  // 确保只处理节点对象，不处理边缘线对象
  if (node && node.isNode && node.isNode()) {
    console.log('NodeConfigPanel: 节点变化', node);
    try {
      // 获取当前节点的样式
      const attrs = node.getAttrs();
      console.log('节点属性:', attrs);

      const body = attrs.body || {};
      const text = attrs.text || {};

      // 获取节点尺寸
      let size = { width: 120, height: 60 };
      try {
        // 安全地获取节点尺寸
        if (node.isNode && node.isNode() && typeof node.size === 'function') {
          size = node.size();
        } else if (node.size) {
          // 如果 size 是一个对象而不是方法
          size = node.size;
        }
      } catch (sizeError) {
        console.warn('获取节点尺寸失败，使用默认值:', sizeError);
      }

      // 获取节点文本
      let labelText = text.text || '';

      // 更新样式配置
      nodeStyle.value = {
        stroke: body.stroke || '#5F95FF',
        strokeWidth: body.strokeWidth || 2,
        fill: body.fill || '#EFF7FF',
        fontSize: text.fontSize || 14,
        fontColor: text.fill || '#333333',
        labelText: labelText,
        width: size.width || 120,
        height: size.height || 60,
        rx: body.rx || 8,
        ry: body.ry || 8
      };

      // 获取当前节点的业务配置数据
      const data = node.getData() || {};
      console.log('节点业务数据:', data);

      // 如果节点已有业务配置，则使用它，否则使用默认值
      businessConfig.value = {
        activityId: data.activityId || '', // 活动ID字段
        roleName: data.roleName || '',     // 角色名称
        description: data.description || '' // 备注（角色描述）
      };

      console.log('更新后的配置:', { style: nodeStyle.value, business: businessConfig.value });
    } catch (error) {
      console.error('获取节点配置失败:', error);
    }
  }
}, { immediate: true });

// 监听面板可见性变化
watch(() => props.visible, (visible) => {
  // 确保在面板可见性变化时，节点的交互功能正常
  setTimeout(() => {
    if (graph.instance && graph.instance.options && graph.instance.options.interacting) {
      console.log('面板可见性变化，确保节点交互功能正常:', visible);
      // 重新启用交互功能
      graph.instance.options.interacting.nodeMovable = true;
      graph.instance.options.interacting.edgeMovable = true;
      graph.instance.options.interacting.arrowheadMovable = true;
      graph.instance.options.interacting.vertexMovable = true;
      graph.instance.options.interacting.vertexAddable = true;
      graph.instance.options.interacting.vertexDeletable = true;
    }
  }, 100);
});

// 应用样式到节点
const applyStyle = () => {
  if (!props.selectedNode) return;

  try {
    console.log('应用样式到节点，当前样式:', nodeStyle.value);

    // 使用 updateNodeStyle 方法更新节点样式
    const success = updateNodeStyle(props.selectedNode, nodeStyle.value);

    if (success) {
      console.log('节点样式更新成功');
      // 通知父组件更新完成
      emit('update');
    } else {
      console.error('节点样式更新失败');
    }
  } catch (error) {
    console.error('更新节点样式失败:', error);
  }
};

// 应用业务配置到节点
const applyBusinessConfig = () => {
  if (!props.selectedNode) return;

  try {
    console.log('应用业务配置到节点:', businessConfig.value);

    // 使用 updateNodeData 方法更新节点数据
    const success = updateNodeData(props.selectedNode, businessConfig.value);

    if (success) {
      console.log('节点业务配置更新成功');
      // 通知父组件更新完成
      emit('update');
    } else {
      console.error('节点业务配置更新失败');
    }
  } catch (error) {
    console.error('更新节点业务配置失败:', error);
  }
};

// 这里移除了权限相关的方法，因为我们不再需要它们

// 关闭面板
const closePanel = () => {
  // 确保在关闭面板前，节点的交互功能正常
  if (graph.instance && graph.instance.options && graph.instance.options.interacting) {
    console.log('面板关闭前确保节点交互功能正常');
    // 重新启用交互功能
    graph.instance.options.interacting.nodeMovable = true;
    graph.instance.options.interacting.edgeMovable = true;
    graph.instance.options.interacting.arrowheadMovable = true;
    graph.instance.options.interacting.vertexMovable = true;
    graph.instance.options.interacting.vertexAddable = true;
    graph.instance.options.interacting.vertexDeletable = true;
  }

  emit('close');
};
</script>

<template>
  <div
    class="node-config-panel"
    :class="{ 'visible': visible }"
  >
    <div class="panel-header">
      <h3>节点配置</h3>
      <button
        class="close-btn"
        @click="closePanel"
      >×</button>
    </div>

    <div class="panel-tabs">
      <div
        class="tab"
        :class="{ 'active': activeTab === 'basic' }"
        @click="activeTab = 'basic'"
      >
        基础配置
      </div>
      <div
        class="tab"
        :class="{ 'active': activeTab === 'business' }"
        @click="activeTab = 'business'"
      >
        业务配置
      </div>
    </div>

    <div class="panel-body">
      <!-- 基础配置标签页 -->
      <div
        v-if="activeTab === 'basic'"
        class="tab-content"
      >
        <div class="form-group">
          <label>节点文本</label>
          <div class="text-input-container">
            <input
              type="text"
              v-model="nodeStyle.labelText"
              placeholder="输入节点文本"
              class="text-input"
              @change="applyStyle"
            />
          </div>
        </div>

        <div class="form-group">
          <label>边框颜色</label>
          <div class="color-input-container">
            <input
              type="color"
              v-model="nodeStyle.stroke"
              class="color-input"
              @change="applyStyle"
            />
            <span class="color-value">{{ nodeStyle.stroke }}</span>
          </div>
        </div>

        <div class="form-group">
          <label>填充颜色</label>
          <div class="color-input-container">
            <input
              type="color"
              v-model="nodeStyle.fill"
              class="color-input"
              @change="applyStyle"
            />
            <span class="color-value">{{ nodeStyle.fill }}</span>
          </div>
        </div>

        <div class="form-group">
          <label>边框宽度</label>
          <div class="input-with-unit">
            <input
              type="number"
              v-model.number="nodeStyle.strokeWidth"
              min="1"
              max="10"
              class="number-input"
              @change="applyStyle"
            />
            <span class="unit">px</span>
          </div>
        </div>

        <div class="form-group">
          <label>字体大小</label>
          <div class="input-with-unit">
            <input
              type="number"
              v-model.number="nodeStyle.fontSize"
              min="8"
              max="36"
              class="number-input"
              @change="applyStyle"
            />
            <span class="unit">px</span>
          </div>
        </div>

        <div class="form-group">
          <label>字体颜色</label>
          <div class="color-input-container">
            <input
              type="color"
              v-model="nodeStyle.fontColor"
              class="color-input"
              @change="applyStyle"
            />
            <span class="color-value">{{ nodeStyle.fontColor }}</span>
          </div>
        </div>

        <div class="form-group">
          <label>节点宽度</label>
          <div class="input-with-unit">
            <input
              type="number"
              v-model.number="nodeStyle.width"
              min="50"
              max="500"
              class="number-input"
              @change="applyStyle"
            />
            <span class="unit">px</span>
          </div>
        </div>

        <div class="form-group">
          <label>节点高度</label>
          <div class="input-with-unit">
            <input
              type="number"
              v-model.number="nodeStyle.height"
              min="30"
              max="300"
              class="number-input"
              @change="applyStyle"
            />
            <span class="unit">px</span>
          </div>
        </div>

        <div class="form-group">
          <label>圆角大小</label>
          <div class="input-with-unit">
            <input
              type="number"
              v-model.number="nodeStyle.rx"
              min="0"
              max="50"
              class="number-input"
              @change="nodeStyle.ry = nodeStyle.rx; applyStyle()"
            />
            <span class="unit">px</span>
          </div>
        </div>
      </div>

      <!-- 业务配置标签页 - 只保留活动ID、角色名称和备注 -->
      <div
        v-if="activeTab === 'business'"
        class="tab-content"
      >
        <div class="form-group">
          <label>活动ID</label>
          <div class="text-input-container">
            <input
              type="text"
              v-model="businessConfig.activityId"
              placeholder="输入活动ID"
              class="text-input"
              @change="applyBusinessConfig"
            />
          </div>
        </div>

        <div class="form-group">
          <label>角色配置</label>
          <select
            v-model="businessConfig.roleName"
            @change="applyBusinessConfig"
          >
            <option value="">请选择角色</option>
            <option
              v-for="role in roleList"
              :key="role.value"
              :value="role.value"
            >
              {{ role.label }}
            </option>
          </select>
        </div>

        <div class="form-group">
          <label>备注</label>
          <div class="text-input-container">
            <textarea
              v-model="businessConfig.description"
              placeholder="输入备注信息"
              class="textarea-input"
              @change="applyBusinessConfig"
            ></textarea>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.node-config-panel {
  position: absolute;
  top: 0;
  right: -320px;
  width: 320px;
  height: 100%;
  background-color: #ffffff;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
  transition: right 0.3s ease;
  overflow: hidden;
  border-left: 1px solid #e1e4e8;
}

.node-config-panel.visible {
  right: 0;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(to right, #4a6bbd, #6384d3);
  border-bottom: 1px solid #d1ddf3;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  opacity: 0.8;
  transition: all 0.2s;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
  opacity: 1;
}

.panel-tabs {
  display: flex;
  background-color: #f0f2f5;
  border-bottom: 1px solid #e1e4e8;
}

.tab {
  flex: 1;
  padding: 12px 0;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  transition: all 0.2s;
  border-bottom: 2px solid transparent;
}

.tab:hover {
  color: #4a6bbd;
  background-color: #f9fafc;
}

.tab.active {
  color: #4a6bbd;
  border-bottom-color: #4a6bbd;
  background-color: #fff;
}

.panel-body {
  padding: 20px;
  height: calc(100% - 110px);
  /* 60px header + 50px tabs */
  overflow-y: auto;
  background-color: #f9fafc;
}

.tab-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

select,
.text-input,
.textarea-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fff;
  font-size: 14px;
  color: #333;
  outline: none;
  transition: all 0.3s;
}

select {
  height: 36px;
}

.text-input {
  height: 36px;
}

.textarea-input {
  height: 80px;
  resize: vertical;
}

select:focus,
.text-input:focus,
.textarea-input:focus {
  border-color: #4a6bbd;
  box-shadow: 0 0 0 2px rgba(74, 107, 189, 0.2);
}

.color-input-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.color-input {
  width: 40px;
  height: 40px;
  padding: 0;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.color-input:focus {
  border-color: #4a6bbd;
  box-shadow: 0 0 0 2px rgba(74, 107, 189, 0.2);
}

.color-value {
  font-size: 14px;
  color: #666;
  font-family: monospace;
}

.input-with-unit {
  display: flex;
  align-items: center;
  position: relative;
}

.number-input {
  width: 100%;
  height: 36px;
  padding: 0 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fff;
  font-size: 14px;
  color: #333;
  outline: none;
  transition: all 0.3s;
  padding-right: 30px;
  /* 为单位留出空间 */
}

.number-input:focus {
  border-color: #4a6bbd;
  box-shadow: 0 0 0 2px rgba(74, 107, 189, 0.2);
}

.unit {
  position: absolute;
  right: 12px;
  font-size: 14px;
  color: #666;
}

.permissions-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.permission-item {
  display: flex;
  align-items: center;
  padding: 6px 10px;
  background-color: #f0f2f5;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.permission-item:hover {
  background-color: #e6f7ff;
}

.permission-item.active {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
}

.permission-checkbox {
  width: 16px;
  height: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  margin-right: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox-inner {
  width: 10px;
  height: 10px;
  background-color: #1890ff;
  border-radius: 1px;
}

.permission-label {
  font-size: 13px;
}

.toggle-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.toggle-switch {
  position: relative;
  width: 40px;
  height: 20px;
  background-color: #ccc;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s;
}

.toggle-switch.active {
  background-color: #1890ff;
}

.toggle-slider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 16px;
  height: 16px;
  background-color: white;
  border-radius: 50%;
  transition: all 0.3s;
}

.toggle-switch.active .toggle-slider {
  left: 22px;
}

.toggle-label {
  font-size: 14px;
  color: #333;
}
</style>
