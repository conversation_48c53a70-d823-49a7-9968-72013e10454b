<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { useFlowGraph } from '../../composables/useFlowGraph';
import { setupNodeTools, hideAllPorts, removeAllNodeTools } from '../../utils/flowUtils';
import { stencilNodes } from '../../constants/stencilNodes'; // 引入为 Stencil 专门设计的节点
import EdgeConfigPanel from './EdgeConfigPanel.vue'; // 引入边缘线配置面板
import NodeConfigPanel from './NodeConfigPanel.vue'; // 引入节点配置面板

// 添加 props 定义来接收 sidebarVisible 属性
const props = defineProps({
  sidebarVisible: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['select']);

const canvasRef = ref(null);
const miniMapRef = ref(null);
const stencilRef = ref(null);
const selectedCell = ref(null);
const isEdgeStylePanelVisible = ref(false);
const isNodeConfigPanelVisible = ref(false); // 合并后的节点配置面板
const nodeCount = ref(0);
const edgeCount = ref(0);

// 获取节点数量
const getNodeCount = () => {
  if (graph.instance) {
    const count = graph.instance.getNodes().length;
    nodeCount.value = count;
    return count;
  }
  return 0;
};

// 获取边数量
const getEdgeCount = () => {
  if (graph.instance) {
    const count = graph.instance.getEdges().length;
    edgeCount.value = count;
    return count;
  }
  return 0;
};

// 处理边缘线样式面板关闭
const handleCloseEdgeStylePanel = () => {
  isEdgeStylePanelVisible.value = false;
};

// 处理节点配置面板关闭
const handleCloseNodeConfigPanel = () => {
  isNodeConfigPanelVisible.value = false;

  // 确保面板关闭后节点交互功能正常
  setTimeout(() => {
    if (graph.instance && graph.instance.options && graph.instance.options.interacting) {
      console.log('面板关闭后重新启用节点交互功能');
      // 重新启用交互功能
      graph.instance.options.interacting.nodeMovable = true;
      graph.instance.options.interacting.edgeMovable = true;
      graph.instance.options.interacting.arrowheadMovable = true;
      graph.instance.options.interacting.vertexMovable = true;
      graph.instance.options.interacting.vertexAddable = true;
      graph.instance.options.interacting.vertexDeletable = true;
    }
  }, 100);
};

// 处理边缘线样式更新
const handleEdgeStyleUpdate = () => {
  console.log('边缘线样式已更新');
  // 更新统计信息
  getNodeCount();
  getEdgeCount();
};

// 处理节点配置更新
const handleNodeConfigUpdate = () => {
  console.log('节点配置已更新');
  // 更新统计信息
  getNodeCount();
  getEdgeCount();
};

const {
  graph,
  initGraph,
  initStencil,
  initMinimap
} = useFlowGraph();

// 确保DOM渲染完成
const waitForDOM = () => {
  return new Promise(resolve => {
    setTimeout(resolve, 200);
  });
};

// 初始化流程图
onMounted(async () => {
  if (!canvasRef.value) return;

  console.log('开始初始化画布...');

  // 初始化画布
  const g = initGraph(canvasRef.value);
  console.log('画布初始化完成，graph.instance:', graph.instance);

  // 等待DOM更新完成
  await nextTick();
  await waitForDOM();

  // 初始化节点模板库 - 使用我们为 Stencil 插件设计的节点
  // 只有在非锁定模式下才初始化节点模板库
  if (stencilRef.value && props.sidebarVisible && !graph.locked) {
    initStencil(stencilRef.value, stencilNodes);
    stencilInitialized.value = true;
    console.log('节点模板库初始化完成');
  }

  // 初始化小地图
  if (miniMapRef.value) {
    initMinimap(miniMapRef.value);
    console.log('小地图初始化完成');
  }

  // 设置节点工具
  setupNodeTools(g);
  console.log('节点工具设置完成');

  // 确保连接桩初始状态为隐藏
  const container = document.getElementById('graph-container');
  if (container) {
    const ports = container.querySelectorAll('.x6-port-body');
    for (let i = 0, len = ports.length; i < len; i += 1) {
      ports[i].style.visibility = 'hidden';
    }
    console.log('连接桩初始状态已设置为隐藏');
  }

  // 监听节点选择事件
  g.on('cell:click', ({ cell }) => {
    selectedCell.value = cell;
    emit('select', cell);

    // 如果选中的是边缘线，交由专门的edge:click事件处理
    if (cell && typeof cell.isEdge === 'function' && cell.isEdge()) {
      console.log('cell:click 选中了边缘线:', cell.id);
      // 不在这里处理边缘线面板显示，由edge:click事件处理
      // 注意：这里不设置面板状态，避免与edge:click事件冲突
    } else if (cell && typeof cell.isNode === 'function' && cell.isNode()) {
      console.log('选中了节点:', cell.id);
      // 点击节点时显示节点配置面板
      isNodeConfigPanelVisible.value = true;
      isEdgeStylePanelVisible.value = false;

      // 确保节点仍然可以交互
      setTimeout(() => {
        if (g && g.options && g.options.interacting) {
          console.log('确保节点交互功能正常');
          // 重新启用必要的交互功能，禁用vertices相关功能
          g.options.interacting.nodeMovable = true;
          g.options.interacting.edgeMovable = true;
          g.options.interacting.arrowheadMovable = true;
          g.options.interacting.vertexMovable = false;
          g.options.interacting.vertexAddable = false;
          g.options.interacting.vertexDeletable = false;
        }
      }, 100);
    } else {
      isEdgeStylePanelVisible.value = false;
      isNodeConfigPanelVisible.value = false;
    }
  });

  // 专门监听边缘线点击事件 - 点击时显示样式面板
  g.on('edge:click', ({ edge }) => {
    console.log('边缘线点击事件触发:', edge.id);
    selectedCell.value = edge;
    // 点击时显示边缘线配置面板
    isEdgeStylePanelVisible.value = true;
    isNodeConfigPanelVisible.value = false; // 确保关闭节点配置面板
  });

  // 监听画布内容变化，更新统计信息
  g.on('cell:added', () => {
    getNodeCount();
    getEdgeCount();
  });

  g.on('cell:removed', () => {
    getNodeCount();
    getEdgeCount();
  });

  // 监听节点移动事件，更新统计信息
  g.on('node:moved', () => {
    getNodeCount();
    getEdgeCount();
  });

  // 监听边缘线变化事件
  g.on('edge:connected', () => {
    getNodeCount();
    getEdgeCount();
  });

  // 初始化统计信息
  getNodeCount();
  getEdgeCount();

  // 监听画布空白区域点击
  g.on('blank:click', () => {
    selectedCell.value = null;
    isEdgeStylePanelVisible.value = false;
    isNodeConfigPanelVisible.value = false;
    emit('select', null);

    // 移除所有节点的删除按钮
    removeAllNodeTools(g);

    // 隐藏所有连接桩
    hideAllPorts();
  });

  // 双击节点事件 - 可以用于其他功能
  g.on('node:dblclick', ({ node }) => {
    console.log('节点双击事件触发:', node.id);
    // 双击节点不再打开角色配置面板，因为已经合并到单击配置面板中
  });

  // 双击边缘线时编辑文本
  g.on('edge:dblclick', ({ edge }) => {
    console.log('边缘线双击事件触发:', edge.id);

    // 获取当前文本
    let currentText = '';
    try {
      if (edge.getLabels && edge.getLabels().length > 0) {
        const labels = edge.getLabels();
        currentText = labels[0]?.attrs?.label?.text || '';
      } else {
        currentText = edge.attr('label/text') || '';
      }
    } catch (err) {
      console.warn('获取边缘线文本失败:', err);
    }

    // 使用浏览器prompt编辑文本
    const newText = prompt('编辑连线文本', currentText);
    if (newText !== null) {
      try {
        // 更新文本
        if (edge.getLabels && edge.getLabels().length > 0) {
          edge.setLabelAt(0, {
            attrs: {
              label: {
                text: newText
              }
            }
          });
        } else {
          edge.attr('label/text', newText);
        }
      } catch (err) {
        console.error('更新边缘线文本失败:', err);
        // 备用方法
        edge.attr('label/text', newText);
      }
    }

    // 阻止事件冒泡，避免与其他事件冲突
    return false;
  });

  console.log('所有事件监听器设置完成');
});

// 标记 stencil 是否已初始化
const stencilInitialized = ref(false);

// 监听侧边栏可见性变化
watch(() => props.sidebarVisible, async (visible) => {
  console.log('侧边栏可见性变化:', visible);

  if (visible) {
    // 当侧边栏从隐藏变为显示时
    console.log('侧边栏显示');

    // 等待DOM更新完成
    await nextTick();

    // 如果 stencil 尚未初始化且不在锁定模式下，则初始化
    if (!stencilInitialized.value && stencilRef.value && graph.instance && !graph.locked) {
      console.log('初始化节点模板库');

      // 初始化 Stencil
      initStencil(stencilRef.value, stencilNodes);
      stencilInitialized.value = true;
      console.log('节点模板库初始化完成');
    } else {
      console.log('节点模板库已初始化或处于锁定模式，无需重新初始化');
    }
  }
}, { immediate: true });

// 组件卸载时清理资源
onUnmounted(() => {
  if (graph.instance) {
    graph.instance.dispose();
    console.log('FlowCanvas: 组件卸载，图形实例已销毁');
  }
});
</script>

<template>
  <div class="flow-workspace">
    <!-- 节点模板库容器 - 根据 sidebarVisible 和锁定状态控制显示 -->
    <div
      v-show="sidebarVisible && !graph.locked"
      ref="stencilRef"
      class="stencil-container"
    ></div>

    <!-- 画布容器 -->
    <div
      class="flow-canvas"
      :class="{ 'full-width': !sidebarVisible || graph.locked }"
    >
      <div
        ref="canvasRef"
        id="graph-container"
        class="canvas-container"
      ></div>

      <!-- 小地图容器 -->
      <div
        ref="miniMapRef"
        class="minimap-container"
      ></div>

      <!-- 画布状态提示 -->
      <div class="canvas-status">
        <div class="status-item">
          <span class="status-label">状态:</span>
          <span
            class="status-value"
            :class="{ 'locked': graph.locked }"
          >
            {{ graph.locked ? '锁定模式' : (graph.instance ? '就绪' : '初始化中') }}
          </span>
        </div>
        <div class="status-item">
          <span class="status-label">节点:</span>
          <span class="status-value">{{ nodeCount }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">连线:</span>
          <span class="status-value">{{ edgeCount }}</span>
        </div>
      </div>
    </div>

    <!-- 边缘线样式配置面板 -->
    <EdgeConfigPanel
      :visible="isEdgeStylePanelVisible"
      :selected-edge="selectedCell"
      @close="handleCloseEdgeStylePanel"
      @update="handleEdgeStyleUpdate"
    />

    <!-- 节点配置面板（合并了属性和角色配置） -->
    <NodeConfigPanel
      :visible="isNodeConfigPanelVisible"
      :selected-node="selectedCell"
      @close="handleCloseNodeConfigPanel"
      @update="handleNodeConfigUpdate"
    />
  </div>
</template>

<style scoped>
.flow-workspace {
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
  overflow: hidden;
}

.stencil-container {
  width: 320px;
  height: 100%;
  position: relative;
  background-color: #f7f9fc;
  border-right: 1px solid #e1e4e8;
  overflow: hidden;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.07);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

/* 以下是Stencil插件内部样式覆盖 */
:deep(.x6-widget-stencil) {
  background-color: #f7f9fc !important;
  box-shadow: none !important;
  border: none !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

:deep(.x6-widget-stencil-title) {
  display: none !important;
  align-items: center !important;
  justify-content: center !important;
  height: 48px !important;
  background: linear-gradient(to right, #4a6bbd, #6384d3) !important;
  padding: 8px 16px !important;
  font-weight: 600 !important;
  color: white !important;
  border-bottom: 1px solid #d1ddf3 !important;
  font-size: 15px !important;
  letter-spacing: 0.5px !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

:deep(.x6-widget-stencil-search) {
  padding: 16px !important;
  /* height: auto !important; */
  height: 96px;
  border-bottom: 1px solid #e1e4e8 !important;
  background-color: #fff !important;
  position: relative !important;
}

:deep(.x6-widget-stencil-search input) {
  height: 40px !important;
  border-radius: 6px !important;
  border: 1px solid #d9d9d9 !important;
  padding: 8px 12px 8px 36px !important;
  /* 增加左侧内边距，为搜索图标留出空间 */
  transition: all 0.3s !important;
  font-size: 14px !important;
  /* width: 100% !important; */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05) !important;
}

/* 添加搜索图标 */
:deep(.x6-widget-stencil-search::before) {
  content: '\1F50D' !important;
  /* Unicode 搜索图标 */
  position: absolute !important;
  left: 40px !important;
  top: 40% !important;
  transform: translateY(-50%) !important;
  font-size: 16px !important;
  color: #999 !important;
  z-index: 9 !important;
}

:deep(.x6-widget-stencil-search input:focus) {
  border-color: #4a6bbd !important;
  box-shadow: 0 0 0 2px rgba(74, 107, 189, 0.2) !important;
}

:deep(.x6-widget-stencil-content) {
  flex: 1 !important;
  overflow: auto !important;
}

:deep(.x6-widget-stencil-not-found) {
  padding: 20px !important;
  text-align: center !important;
  color: #666 !important;
  font-size: 14px !important;
  background-color: #f7f9fc !important;
  height: auto !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-height: 100px !important;
}

:deep(.x6-widget-stencil-group-content) {
  display: grid !important;
  grid-template-columns: repeat(2, 1fr) !important;
  grid-gap: 20px !important;
  background-color: #f7f9fc !important;
  justify-items: center !important;
  align-items: center !important;
  min-height: 200px !important;
}

:deep(.x6-widget-stencil-group > .x6-widget-stencil-group-title) {
  padding: 12px 16px !important;
  background-color: #e7eefa !important;
  color: #4a6bbd !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-top: 1px solid #d1ddf3 !important;
  border-bottom: 1px solid #d1ddf3 !important;
}

:deep(.x6-widget-transform) {
  margin: 0 !important;
}

:deep(.x6-node) {
  cursor: move !important;
  filter: none !important;
  transition: all 0.2s ease !important;
}

:deep(.x6-node:hover) {
  opacity: 0.9 !important;
}

:deep(.x6-widget-stencil-group-content .x6-graph-svg) {
  overflow: visible !important;
}

.flow-canvas {
  flex: 1;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.flow-canvas.full-width {
  width: 100%;
}

.canvas-container {
  width: 100%;
  height: 100%;
  position: relative;
  /* background-color: #f9fafc; */
  flex: 1;
}

.minimap-container {
  position: absolute;
  right: 20px;
  bottom: 80px;
  width: 200px;
  height: 160px;
  background-color: rgba(255, 255, 255, 0.95);
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.canvas-status {
  position: absolute;
  left: 20px;
  bottom: 20px;
  display: flex;
  gap: 16px;
  background-color: rgba(255, 255, 255, 0.95);
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 8px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  font-size: 14px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-label {
  color: #666;
  font-weight: 500;
}

.status-value {
  color: #333;
  font-weight: 600;
}

.status-value.locked {
  color: #ff6b6b;
  font-weight: 600;
}
</style>