import { ref, reactive } from 'vue';
import { getRoleList, getAssigneeStrategies } from '../apis/api.js';

// 创建单例状态
const sharedState = {
  // 角色列表
  roleList: ref([]),
  // 选人策略列表
  assigneeStrategies: ref([]),
  // 加载状态
  loading: reactive({
    roles: false,
    strategies: false
  }),
  // 初始化状态
  initialized: reactive({
    roles: false,
    strategies: false
  })
};

/**
 * 配置选项管理组合式函数
 * 负责管理角色列表和选人策略等下拉选项数据
 */
export function useConfigOptions () {
  const { roleList, assigneeStrategies, loading, initialized } = sharedState;

  /**
   * 获取角色列表
   */
  const fetchRoleList = async () => {
    if (initialized.roles || loading.roles) {
      return roleList.value;
    }
    try {
      loading.roles = true;
      console.log('开始获取角色列表...');

      const response = await getRoleList();
      const { code, data, message } = response;

      if (code === '0000' && data) {
        // 处理角色数据，确保格式为 { value, label }
        if (Array.isArray(data)) {
          roleList.value = data.map(item => {
            return {
              value: item.value || item.id || item.code,
              label: item.label || item.name || item.title
            };
          });
        } else {
          console.warn('角色数据格式不正确，使用默认值');
        }

        initialized.roles = true;
        console.log('角色列表获取成功:', roleList.value);
      } else {
        console.error('获取角色列表失败:', message);
        initialized.roles = true;
      }
    } catch (error) {
      console.error('获取角色列表时发生错误:', error);
      // 使用默认角色列表
      initialized.roles = true;
    } finally {
      loading.roles = false;
    }

    return roleList.value;
  };

  /**
   * 获取选人策略列表
   */
  const fetchAssigneeStrategies = async () => {
    if (initialized.strategies || loading.strategies) {
      return assigneeStrategies.value;
    }

    try {
      loading.strategies = true;
      console.log('开始获取选人策略列表...');

      const response = await getAssigneeStrategies();
      const { code, data, message } = response;

      if (code === '0000' && data) {
        // 处理策略数据，确保格式为 { value, label }
        if (Array.isArray(data)) {
          assigneeStrategies.value = data.map(item => {
            return {
              value: item.value || item.id || item.code,
              label: item.label || item.name || item.title
            };
          });
        } else {
          console.warn('选人策略数据格式不正确，使用默认值');
        }

        initialized.strategies = true;
        console.log('选人策略列表获取成功:', assigneeStrategies.value);
      } else {
        console.error('获取选人策略列表失败:', message);
        // 使用默认策略列表
        initialized.strategies = true;
      }
    } catch (error) {
      console.error('获取选人策略列表时发生错误:', error);
      // 使用默认策略列表
      initialized.strategies = true;
    } finally {
      loading.strategies = false;
    }

    return assigneeStrategies.value;
  };

  /**
   * 初始化所有配置选项
   */
  const initializeOptions = async () => {
    console.log('开始初始化配置选项...');

    try {
      // 并行获取角色列表和选人策略列表
      await Promise.all([
        fetchRoleList(),
        fetchAssigneeStrategies()
      ]);

      console.log('配置选项初始化完成');
    } catch (error) {
      console.error('初始化配置选项时发生错误:', error);
    }
  };

  /**
   * 获取默认角色列表
   */
  const getDefaultRoles = () => {
    return [
      { value: 'admin', label: '管理员' },
      { value: 'manager', label: '经理' },
      { value: 'employee', label: '员工' },
      { value: 'finance', label: '财务' },
      { value: 'hr', label: '人事' },
      { value: 'it', label: 'IT支持' }
    ];
  };

  /**
   * 获取默认选人策略列表
   */
  const getDefaultStrategies = () => {
    return [
      { value: 'direct', label: '直接指定' },
      { value: 'role', label: '按角色' },
      { value: 'department', label: '按部门' },
      { value: 'superior', label: '上级领导' },
      { value: 'dynamic', label: '动态计算' },
      { value: 'random', label: '随机分配' }
    ];
  };

  /**
   * 重新加载角色列表
   */
  const reloadRoleList = async () => {
    initialized.roles = false;
    return await fetchRoleList();
  };

  /**
   * 重新加载选人策略列表
   */
  const reloadAssigneeStrategies = async () => {
    initialized.strategies = false;
    return await fetchAssigneeStrategies();
  };

  return {
    // 数据
    roleList,
    assigneeStrategies,
    loading,
    initialized,

    // 方法
    fetchRoleList,
    fetchAssigneeStrategies,
    initializeOptions,
    reloadRoleList,
    reloadAssigneeStrategies,
    getDefaultRoles,
  };
}
