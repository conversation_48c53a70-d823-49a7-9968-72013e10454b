import { Graph } from '@antv/x6';

/**
 * 注册流程图自定义节点
 */
import { nodeConfigs } from '../constants/nodeConfigs';

export function registerCustomNodes () {
  // 遍历节点配置，注册所有自定义节点
  Object.entries(nodeConfigs).forEach(([shape, config]) => {
    // 为文本添加通用的定位属性
    const textAttrs = {
      ...config.attrs.text,
      refX: 0.5,
      refY: 0.5,
      textAnchor: 'middle',
      textVerticalAnchor: 'middle',
    };

    // 注册节点
    Graph.registerNode(
      shape,
      {
        inherit: config.inherit,
        width: config.width,
        height: config.height,
        attrs: {
          ...config.attrs,
          text: textAttrs,
        },
        ports: config.ports,
      },
      true
    );
  });
}

/**
 * 控制连接桩显示/隐藏
 * @param {NodeListOf<SVGElement>} ports 连接桩元素集合
 * @param {boolean} show 是否显示
 */
const showPorts = (ports, show) => {
  for (let i = 0, len = ports.length; i < len; i += 1) {
    ports[i].style.visibility = show ? 'visible' : 'hidden';
  }
};

/**
 * 隐藏所有连接桩
 */
export function hideAllPorts () {
  const container = document.getElementById('graph-container');
  if (container) {
    const ports = container.querySelectorAll('.x6-port-body');
    showPorts(ports, false);
  }
};

/**
 * 移除所有节点的删除按钮
 * @param {Graph} graph 图实例
 */
export function removeAllNodeTools (graph) {
  if (!graph) return;

  // 获取所有节点并移除删除按钮
  const nodes = graph.getNodes();
  if (nodes && nodes.length > 0) {
    nodes.forEach(node => {
      node.removeTool('button-remove', { recordHistory: false });
    });
  }
};

/**
 * 为节点添加工具
 * @param {Graph} graph 图实例
 */
export function setupNodeTools (graph) {
  if (!graph) return;

  // 监听节点鼠标进入事件，添加删除按钮并显示连接桩
  graph.on('node:mouseenter', ({ node }) => {
    // 检查是否处于锁定模式
    const isLocked = graph.locked || false;

    // 只有在非锁定模式下才添加删除按钮
    if (!isLocked) {
      // 获取节点的形状
      const shape = node.shape;

      // 根据节点形状设置不同的删除按钮位置
      if (shape === 'flow-rhombus' || shape === 'flow-decision') {
        // 菱形节点 - 将删除按钮放在节点内部的右上角
        node.addTools({
          name: 'button-remove',
          args: {
            x: '50%',  // 水平居中
            y: '0%',   // 顶部
            offset: { x: 20, y: 20 }, // 向右下方偏移
          },
        }, { recordHistory: false });
      } else if (shape === 'flow-circle' || shape === 'flow-start' || shape === 'flow-end') {
        // 圆形节点 - 将删除按钮放在节点内部的右上角
        node.addTools({
          name: 'button-remove',
          args: {
            x: '75%',  // 右侧 3/4 处
            y: '25%',  // 上方 1/4 处
            offset: { x: 0, y: 0 },
          },
        }, { recordHistory: false });
      } else {
        // 默认矩形节点 - 将删除按钮放在节点内部的右上角
        node.addTools({
          name: 'button-remove',
          args: {
            x: '100%',
            y: '0%',
            offset: { x: -20, y: 20 }, // 向左下方偏移，确保在节点内部
          },
        }, { recordHistory: false });
      }
    }

    // 只有在非锁定模式下才显示连接桩
    if (!isLocked) {
      const container = document.getElementById('graph-container');
      if (container) {
        const ports = container.querySelectorAll('.x6-port-body');
        showPorts(ports, true);
      }
    }
  });

  // 监听节点鼠标离开事件，移除删除按钮并隐藏连接桩
  graph.on('node:mouseleave', ({ node }) => {
    node.removeTool('button-remove', { recordHistory: false });
    // 隐藏所有连接桩
    hideAllPorts();
  });

  // 监听边鼠标进入事件，添加边工具
  graph.on('edge:mouseenter', ({ edge }) => {
    // 检查是否处于锁定模式
    const isLocked = graph.locked || false;

    // 只有在非锁定模式下才添加删除按钮
    if (!isLocked) {
      edge.addTools([
        {
          name: 'button-remove',
          args: {
            distance: -40,
          },
        },
        // 暂时注释掉边缘线调整工具，避免与边缘线配置面板点击事件冲突
        // {
        //   name: 'vertices',
        // },
        // {
        //   name: 'segments',
        // },
      ], { recordHistory: false });
    }
  });

  // 监听边鼠标离开事件，移除边工具
  graph.on('edge:mouseleave', ({ edge }) => {
    edge.removeTools({ recordHistory: false });
  });

  // 监听连线开始事件，显示所有连接桩
  graph.on('edge:connected', () => {
    const container = document.getElementById('graph-container');
    if (container) {
      const ports = container.querySelectorAll('.x6-port-body');
      showPorts(ports, false);
    }
  });

  // 监听连线过程中事件，显示所有连接桩
  graph.on('edge:connecting', () => {
    const container = document.getElementById('graph-container');
    if (container) {
      const ports = container.querySelectorAll('.x6-port-body');
      showPorts(ports, true);
    }
  });

  // 边的vertices变化事件监听已移除
}

/**
 * 生成随机ID
 */
export function generateId () {
  return `node_${Math.random().toString(36).substring(2, 11)}`;
}