<script setup>
import { ref, watch } from 'vue';
import { useFlowGraph } from '../../composables/useFlowGraph';
import { useConfigOptions } from '../../composables/useConfigOptions';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  selectedEdge: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['close', 'update']);

const { updateEdgeStyle, updateEdgeData } = useFlowGraph();

// 获取配置选项
const { assigneeStrategies } = useConfigOptions();

// 当前激活的标签页
const activeTab = ref('basic'); // 'basic' 或 'business'

// 边缘线样式配置
const edgeStyle = ref({
  stroke: '#5F95FF',
  strokeWidth: 2,
  strokeDasharray: '',
  targetMarker: 'classic',
  sourceMarker: 'none',
  markerSize: 8,
  lineType: 'normal', // 使用 normal 作为默认直线类型
  labelText: '', // 添加标签文本属性
  fontColor: '#333333', // 添加字体颜色属性
  fontSize: 12 // 添加字体大小属性
});

// 可选的线条类型
const lineTypes = [
  { value: 'normal', label: '直线' },
  { value: 'rounded', label: '圆角' },
  { value: 'smooth', label: '平滑曲线' },
  { value: 'jumpover', label: '跳线' }
];

// 可选的箭头类型
const markerTypes = [
  { value: 'none', label: '无' },
  { value: 'classic', label: '经典箭头' },
  { value: 'diamond', label: '菱形' },
  { value: 'circle', label: '圆形' },
  { value: 'path', label: '自定义' }
];

// 可选的线条样式
const dashArrays = [
  { value: '', label: '实线' },
  { value: '5,5', label: '虚线' },
  { value: '10,5', label: '长虚线' },
  { value: '5,5,1,5', label: '点划线' }
];

// 移除了静态的线条颜色数组，改为使用颜色选择器

// 业务配置数据
const businessConfig = ref({
  flowExpression: '', // 流转表达式
  flowVariables: '', // 流转变量
  assigneeStrategy: 'direct' // 选人策略
});

// 移除了静态的选人策略数组，改为从接口获取

// 监听选中的边缘线变化
watch(() => props.selectedEdge, (edge) => {
  // 确保只处理边缘线对象，不处理节点对象
  if (edge && edge.isEdge && edge.isEdge()) {
    console.log('EdgeStylePanel: 边缘线变化', edge);
    try {
      // 获取当前边缘线的样式
      const attrs = edge.getAttrs();
      console.log('边缘线属性:', attrs);

      const line = attrs.line || {};
      let sourceMarker = typeof line.sourceMarker === 'object' ? line.sourceMarker : { name: line.sourceMarker };
      let targetMarker = typeof line.targetMarker === 'object' ? line.targetMarker : { name: line.targetMarker };

      if (!sourceMarker) sourceMarker = { name: 'none' };
      if (!targetMarker) targetMarker = { name: 'classic' };

      // 获取连接器类型
      let connectorName = 'rounded';
      try {
        // X6 的 Edge 对象可能没有 getConnector 方法，所以我们从 connector 属性获取
        const connector = edge.connector || edge.getConnector?.();
        if (connector && connector.name) {
          connectorName = connector.name;
        } else if (typeof connector === 'string') {
          connectorName = connector;
        }
      } catch (err) {
        console.warn('获取连接器类型失败:', err);
      }

      // 获取边缘线文本
      let labelText = '';
      try {
        // 安全地获取标签
        if (edge.getLabels) {
          const labels = edge.getLabels();
          if (labels && labels.length > 0) {
            labelText = labels[0]?.attrs?.label?.text || '';
          }
        } else {
          // 如果没有getLabels方法，尝试从attr获取文本
          labelText = edge.attr('label/text') || '';
        }
      } catch (err) {
        console.warn('获取边缘线文本失败:', err);
        // 尝试从attr获取文本作为备用
        try {
          labelText = edge.attr('label/text') || '';
        } catch (attrErr) {
          console.warn('从attr获取文本也失败:', attrErr);
        }
      }

      // 获取标签字体颜色
      let fontColor = '#333333';
      try {
        if (edge.getLabels && edge.getLabels().length > 0) {
          const labels = edge.getLabels();
          fontColor = labels[0]?.attrs?.label?.fill || '#333333';
        } else {
          fontColor = edge.attr('label/fill') || '#333333';
        }
      } catch (err) {
        console.warn('获取字体颜色失败:', err);
      }

      // 获取标签字体大小
      let fontSize = 12;
      try {
        if (edge.getLabels && edge.getLabels().length > 0) {
          const labels = edge.getLabels();
          fontSize = labels[0]?.attrs?.label?.fontSize || 12;
        } else {
          fontSize = edge.attr('label/fontSize') || 12;
        }
      } catch (err) {
        console.warn('获取字体大小失败:', err);
      }

      // 更新样式配置
      edgeStyle.value = {
        stroke: line.stroke || '#5F95FF',
        strokeWidth: line.strokeWidth || 2,
        strokeDasharray: line.strokeDasharray || '',
        targetMarker: targetMarker.name || 'classic',
        sourceMarker: sourceMarker.name || 'none',
        markerSize: targetMarker.size || 8,
        lineType: connectorName,
        labelText: labelText,
        fontColor: fontColor,
        fontSize: fontSize
      };

      // 获取当前边缘线的业务配置数据
      const data = edge.getData() || {};
      console.log('边缘线业务数据:', data);

      // 如果边缘线已有业务配置，则使用它，否则使用默认值
      businessConfig.value = {
        flowExpression: data.flowExpression || '',
        flowVariables: data.flowVariables || '',
        assigneeStrategy: data.assigneeStrategy || 'direct'
      };

      console.log('更新后的样式配置:', edgeStyle.value);
      console.log('更新后的业务配置:', businessConfig.value);
    } catch (error) {
      console.error('获取边缘线配置失败:', error);
    }
  }
}, { immediate: true });

// 更新边缘线文本
const updateEdgeLabel = () => {
  if (!props.selectedEdge) return;

  try {
    const edge = props.selectedEdge;
    const text = edgeStyle.value.labelText;

    try {
      // 安全地获取标签
      const labels = edge.getLabels ? edge.getLabels() : [];

      if (labels && labels.length > 0) {
        edge.setLabelAt(0, {
          attrs: {
            label: {
              text: text,
              fill: edgeStyle.value.fontColor,
              fontSize: edgeStyle.value.fontSize,
            },
          },
        });
      } else if (text) {
        // 如果没有标签但有文本，则添加新标签
        if (edge.appendLabel) {
          edge.appendLabel({
            attrs: {
              label: {
                text: text,
                fill: edgeStyle.value.fontColor,
                fontSize: edgeStyle.value.fontSize,
              },
            },
            position: {
              distance: 0.5,
              options: {
                absoluteDistance: true,
                reverseDistance: true,
                keepGradient: true,
                ensureLegibility: true,
              }
            },
          });
        } else {
          // 如果没有appendLabel方法，则使用attr方法设置文本
          edge.attr('label/text', text);
          edge.attr('label/fill', edgeStyle.value.fontColor);
          edge.attr('label/fontSize', edgeStyle.value.fontSize);
        }
      }
    } catch (labelError) {
      console.warn('获取或设置标签失败，尝试使用attr方法:', labelError);
      // 备用方法：直接设置文本属性
      edge.attr('label/text', text);
      edge.attr('label/fill', edgeStyle.value.fontColor);
      edge.attr('label/fontSize', edgeStyle.value.fontSize);
    }

    // 通知父组件更新完成
    emit('update');
  } catch (error) {
    console.error('更新边缘线文本失败:', error);
  }
};

// 应用样式到边缘线
const applyStyle = () => {
  if (!props.selectedEdge) return;

  // 更新边缘线样式
  updateEdgeStyle(props.selectedEdge, edgeStyle.value);

  // 更新边缘线文本
  updateEdgeLabel();

  // 通知父组件更新完成
  emit('update');
};

// 应用业务配置到边缘线
const applyBusinessConfig = () => {
  if (!props.selectedEdge) return;

  try {
    console.log('应用业务配置到边缘线:', businessConfig.value);

    // 使用 updateEdgeData 方法更新边缘线数据
    const success = updateEdgeData(props.selectedEdge, businessConfig.value);

    if (success) {
      console.log('边缘线业务配置更新成功');
      // 通知父组件更新完成
      emit('update');
    } else {
      console.error('边缘线业务配置更新失败');
    }
  } catch (error) {
    console.error('更新边缘线业务配置失败:', error);
  }
};

// 关闭面板
const closePanel = () => {
  emit('close');
};
</script>

<template>
  <div
    class="edge-style-panel"
    :class="{ 'visible': visible }"
  >
    <div class="panel-header">
      <h3>边缘线配置</h3>
      <button
        class="close-btn"
        @click="closePanel"
      >×</button>
    </div>

    <div class="panel-tabs">
      <div
        class="tab"
        :class="{ 'active': activeTab === 'basic' }"
        @click="activeTab = 'basic'"
      >
        基础配置
      </div>
      <div
        class="tab"
        :class="{ 'active': activeTab === 'business' }"
        @click="activeTab = 'business'"
      >
        业务配置
      </div>
    </div>

    <div class="panel-body">
      <!-- 基础配置标签页 -->
      <div
        v-if="activeTab === 'basic'"
        class="tab-content"
      >
        <div class="form-group">
          <label>线条类型</label>
          <select
            v-model="edgeStyle.lineType"
            @change="applyStyle"
          >
            <option
              v-for="type in lineTypes"
              :key="type.value"
              :value="type.value"
            >
              {{ type.label }}
            </option>
          </select>
        </div>

        <div class="form-group">
          <label>线条颜色</label>
          <div class="color-input-container">
            <input
              type="color"
              v-model="edgeStyle.stroke"
              class="color-input"
              @change="applyStyle"
            />
            <span class="color-value">{{ edgeStyle.stroke }}</span>
          </div>
        </div>

        <div class="form-group">
          <label>线条宽度</label>
          <div class="slider-container">
            <input
              type="range"
              v-model.number="edgeStyle.strokeWidth"
              min="1"
              max="6"
              step="1"
              @change="applyStyle"
            />
            <span class="slider-value">{{ edgeStyle.strokeWidth }}px</span>
          </div>
        </div>

        <div class="form-group">
          <label>线条样式</label>
          <select
            v-model="edgeStyle.strokeDasharray"
            @change="applyStyle"
          >
            <option
              v-for="dash in dashArrays"
              :key="dash.value"
              :value="dash.value"
            >
              {{ dash.label }}
            </option>
          </select>
        </div>

        <div class="form-group">
          <label>起点箭头</label>
          <select
            v-model="edgeStyle.sourceMarker"
            @change="applyStyle"
          >
            <option
              v-for="marker in markerTypes"
              :key="marker.value"
              :value="marker.value"
            >
              {{ marker.label }}
            </option>
          </select>
        </div>

        <div class="form-group">
          <label>终点箭头</label>
          <select
            v-model="edgeStyle.targetMarker"
            @change="applyStyle"
          >
            <option
              v-for="marker in markerTypes"
              :key="marker.value"
              :value="marker.value"
            >
              {{ marker.label }}
            </option>
          </select>
        </div>

        <div class="form-group">
          <label>箭头大小</label>
          <div class="slider-container">
            <input
              type="range"
              v-model.number="edgeStyle.markerSize"
              min="4"
              max="16"
              step="1"
              @change="applyStyle"
            />
            <span class="slider-value">{{ edgeStyle.markerSize }}px</span>
          </div>
        </div>

        <div class="form-group">
          <label>连线文本</label>
          <div class="text-input-container">
            <input
              type="text"
              v-model="edgeStyle.labelText"
              placeholder="输入连线文本"
              class="text-input"
              @change="applyStyle"
            />
          </div>
        </div>

        <div class="form-group">
          <label>文本颜色</label>
          <div class="color-input-container">
            <input
              type="color"
              v-model="edgeStyle.fontColor"
              class="color-input"
              @change="applyStyle"
            />
            <span class="color-value">{{ edgeStyle.fontColor }}</span>
          </div>
        </div>

        <div class="form-group">
          <label>字体大小</label>
          <div class="input-with-unit">
            <input
              type="number"
              v-model.number="edgeStyle.fontSize"
              min="8"
              max="36"
              class="number-input"
              @change="applyStyle"
            />
            <span class="unit">px</span>
          </div>
        </div>
      </div>

      <!-- 业务配置标签页 -->
      <div
        v-if="activeTab === 'business'"
        class="tab-content"
      >
        <div class="form-group">
          <label>流转表达式</label>
          <div class="text-input-container">
            <textarea
              v-model="businessConfig.flowExpression"
              placeholder="输入流转表达式，例如：amount > 1000"
              class="textarea-input"
              @change="applyBusinessConfig"
            ></textarea>
          </div>
        </div>

        <div class="form-group">
          <label>流转变量</label>
          <div class="text-input-container">
            <textarea
              v-model="businessConfig.flowVariables"
              placeholder="输入流转变量，例如：{amount: 1000, type: 'expense'}"
              class="textarea-input"
              @change="applyBusinessConfig"
            ></textarea>
          </div>
        </div>

        <div class="form-group">
          <label>选人策略</label>
          <select
            v-model="businessConfig.assigneeStrategy"
            @change="applyBusinessConfig"
          >
            <option value="">请选择选人策略</option>
            <option
              v-for="strategy in assigneeStrategies"
              :key="strategy.value"
              :value="strategy.value"
            >
              {{ strategy.label }}
            </option>
          </select>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.edge-style-panel {
  position: absolute;
  top: 0;
  right: -320px;
  width: 320px;
  height: 100%;
  background-color: #ffffff;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
  transition: right 0.3s ease;
  overflow: hidden;
  border-left: 1px solid #e1e4e8;
}

.edge-style-panel.visible {
  right: 0;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(to right, #4a6bbd, #6384d3);
  border-bottom: 1px solid #d1ddf3;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  opacity: 0.8;
  transition: all 0.2s;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
  opacity: 1;
}

.panel-tabs {
  display: flex;
  background-color: #f0f2f5;
  border-bottom: 1px solid #e1e4e8;
}

.tab {
  flex: 1;
  padding: 12px 0;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  transition: all 0.2s;
  border-bottom: 2px solid transparent;
}

.tab:hover {
  color: #4a6bbd;
  background-color: #f9fafc;
}

.tab.active {
  color: #4a6bbd;
  border-bottom-color: #4a6bbd;
  background-color: #fff;
}

.panel-body {
  padding: 20px;
  height: calc(100% - 110px);
  /* 60px header + 50px tabs */
  overflow-y: auto;
  background-color: #f9fafc;
}

.tab-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

select {
  width: 100%;
  height: 36px;
  padding: 0 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fff;
  font-size: 14px;
  color: #333;
  outline: none;
  transition: all 0.3s;
}

select:focus {
  border-color: #4a6bbd;
  box-shadow: 0 0 0 2px rgba(74, 107, 189, 0.2);
}

.color-picker {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.color-item {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.2s;
}

.color-item:hover {
  transform: scale(1.1);
}

.color-item.active {
  border-color: #333;
  transform: scale(1.1);
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

input[type="range"] {
  flex: 1;
  height: 6px;
  -webkit-appearance: none;
  appearance: none;
  background-color: #e1e4e8;
  border-radius: 3px;
  outline: none;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #4a6bbd;
  cursor: pointer;
}

.slider-value {
  min-width: 40px;
  font-size: 14px;
  color: #666;
}

.text-input-container {
  width: 100%;
}

.text-input {
  width: 100%;
  height: 36px;
  padding: 0 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fff;
  font-size: 14px;
  color: #333;
  outline: none;
  transition: all 0.3s;
}

.text-input:focus {
  border-color: #4a6bbd;
  box-shadow: 0 0 0 2px rgba(74, 107, 189, 0.2);
}

.textarea-input {
  width: 100%;
  height: 80px;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fff;
  font-size: 14px;
  color: #333;
  outline: none;
  transition: all 0.3s;
  resize: vertical;
}

.textarea-input:focus {
  border-color: #4a6bbd;
  box-shadow: 0 0 0 2px rgba(74, 107, 189, 0.2);
}

.color-input-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.color-input {
  width: 40px;
  height: 40px;
  padding: 0;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.color-input:focus {
  border-color: #4a6bbd;
  box-shadow: 0 0 0 2px rgba(74, 107, 189, 0.2);
}

.color-value {
  font-size: 14px;
  color: #666;
  font-family: monospace;
}

.input-with-unit {
  display: flex;
  align-items: center;
  gap: 12px;
}

.number-input {
  flex: 1;
  height: 36px;
  padding: 0 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fff;
  font-size: 14px;
  color: #333;
  outline: none;
  transition: all 0.3s;
}

.number-input:focus {
  border-color: #4a6bbd;
  box-shadow: 0 0 0 2px rgba(74, 107, 189, 0.2);
}

.unit {
  font-size: 14px;
  color: #666;
}
</style>
