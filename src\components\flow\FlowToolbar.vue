<script setup>
import { ref, watch, computed } from 'vue';
import { useFlowGraph } from '../../composables/useFlowGraph';

const props = defineProps({
  sidebarVisible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:sidebarVisible', 'exportJson', 'importJson', 'clearGraph', 'saveToBackend']);

const {
  graph,
  zoomIn,
  zoomOut,
  zoomReset,
  centerContent,
  undo,
  exportToJSON,
  canUndo
} = useFlowGraph();

// 画布缩放比例
const scale = ref(1);

// 监听图形缩放
watch(() => graph.instance, (newGraphInstance) => {
  if (newGraphInstance) {
    console.log('FlowToolbar: 监听到graph.instance变化', newGraphInstance);
    newGraphInstance.on('scale', ({ sx }) => {
      scale.value = Math.round(sx * 100) / 100;
    });
  }
}, { immediate: true });

// 计算侧边栏按钮状态
const sidebarBtnClass = computed(() => {
  return props.sidebarVisible ? 'active' : '';
});

// 切换侧边栏
const toggleSidebar = () => {
  emit('update:sidebarVisible', !props.sidebarVisible);
};

// 导出为JSON
const handleExportJson = () => {
  emit('exportJson');
};

// 导入JSON
const handleImportJson = () => {
  emit('importJson');
};

// 保存到后端
const handleSaveToBackend = () => {
  // 发出事件通知父组件，由父组件处理保存到后端的逻辑
  emit('saveToBackend');
};

// 清空画布
const handleClearGraph = () => {
  // 只发出事件通知父组件，不直接调用clearGraph
  emit('clearGraph');
};
</script>

<template>
  <div class="flow-toolbar">
    <div class="toolbar-group">
      <button
        :class="['toolbar-btn', sidebarBtnClass]"
        title="显示/隐藏节点面板"
        @click="toggleSidebar"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <rect
            x="3"
            y="3"
            width="18"
            height="18"
            rx="2"
            ry="2"
          ></rect>
          <line
            x1="9"
            y1="3"
            x2="9"
            y2="21"
          ></line>
        </svg>
      </button>
    </div>

    <div class="toolbar-group">
      <button
        class="toolbar-btn"
        :class="{ 'disabled': !canUndo }"
        title="撤销"
        @click="undo"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <polyline points="9 14 4 9 9 4"></polyline>
          <path d="M20 20v-7a4 4 0 0 0-4-4H4"></path>
        </svg>
      </button>
    </div>

    <div class="toolbar-group">
      <button
        class="toolbar-btn"
        title="居中显示"
        @click="centerContent"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <polygon points="3 11 22 2 13 21 11 13 3 11"></polygon>
        </svg>
      </button>
      <button
        class="toolbar-btn"
        title="缩小"
        @click="zoomOut"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <circle
            cx="11"
            cy="11"
            r="8"
          ></circle>
          <line
            x1="21"
            y1="21"
            x2="16.65"
            y2="16.65"
          ></line>
          <line
            x1="8"
            y1="11"
            x2="14"
            y2="11"
          ></line>
        </svg>
      </button>
      <div class="zoom-display">{{ Math.round(scale * 100) }}%</div>
      <button
        class="toolbar-btn"
        title="放大"
        @click="zoomIn"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <circle
            cx="11"
            cy="11"
            r="8"
          ></circle>
          <line
            x1="21"
            y1="21"
            x2="16.65"
            y2="16.65"
          ></line>
          <line
            x1="11"
            y1="8"
            x2="11"
            y2="14"
          ></line>
          <line
            x1="8"
            y1="11"
            x2="14"
            y2="11"
          ></line>
        </svg>
      </button>
      <button
        class="toolbar-btn"
        title="重置缩放"
        @click="zoomReset"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <circle
            cx="12"
            cy="12"
            r="10"
          ></circle>
          <line
            x1="12"
            y1="8"
            x2="12"
            y2="16"
          ></line>
          <line
            x1="8"
            y1="12"
            x2="16"
            y2="12"
          ></line>
        </svg>
      </button>
    </div>

    <div class="toolbar-group">
      <button
        class="toolbar-btn primary"
        title="导出并保存"
        @click="handleSaveToBackend"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
          <polyline points="17 21 17 13 7 13 7 21"></polyline>
          <polyline points="7 3 7 8 15 8"></polyline>
        </svg>
      </button>
      
      <!-- <button
        class="toolbar-btn"
        title="导出为JSON"
        @click="handleExportJson"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
          <polyline points="7 10 12 15 17 10"></polyline>
          <line
            x1="12"
            y1="15"
            x2="12"
            y2="3"
          ></line>
        </svg>
      </button> -->
      <!-- <button
        class="toolbar-btn"
        title="导入JSON"
        @click="handleImportJson"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
          <polyline points="17 8 12 3 7 8"></polyline>
          <line
            x1="12"
            y1="3"
            x2="12"
            y2="15"
          ></line>
        </svg>
      </button> -->

      <button
        class="toolbar-btn danger"
        title="清空画布"
        @click="handleClearGraph"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <polyline points="3 6 5 6 21 6"></polyline>
          <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
        </svg>
      </button>
    </div>
  </div>
</template>

<style scoped>
.flow-toolbar {
  width: 100%;
  height: 56px;
  background-color: #ffffff;
  background-image: linear-gradient(to right, #ffffff, #f9fafc);
  border-bottom: 1px solid #e1e4e8;
  display: flex;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.toolbar-group {
  display: flex;
  align-items: center;
  margin-right: 24px;
  position: relative;
}

.toolbar-group:not(:last-child)::after {
  content: '';
  position: absolute;
  right: -12px;
  top: 50%;
  transform: translateY(-50%);
  height: 24px;
  width: 1px;
  background-color: #e1e4e8;
}

.toolbar-btn {
  width: 38px;
  height: 38px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #626a73;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0 3px;
  position: relative;
}

.toolbar-btn:hover {
  background-color: #f0f2f5;
  color: #4a6bbd;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.toolbar-btn:active {
  transform: translateY(0);
  box-shadow: none;
}

.toolbar-btn.active {
  background-color: #e7eefa;
  color: #4a6bbd;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toolbar-btn.primary {
  color: #1890ff;
}

.toolbar-btn.primary:hover {
  background-color: #e6f7ff;
  color: #1890ff;
}

.toolbar-btn.danger:hover {
  background-color: #fff1f0;
  color: #f5222d;
}

.zoom-display {
  min-width: 60px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #626a73;
  font-size: 14px;
  font-weight: 500;
  background-color: #f9fafc;
  border-radius: 4px;
  border: 1px solid #e1e4e8;
  margin: 0 4px;
}
</style>