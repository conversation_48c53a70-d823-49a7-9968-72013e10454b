/**
 * 节点公共配置
 * 用于在 stencilNodes.js 和 flowUtils.js 中共享节点配置
 */

// 通用端口样式生成函数
const createPortAttrs = (color) => ({
  circle: {
    r: 5,
    magnet: true,
    stroke: color,
    strokeWidth: 1.5,
    fill: '#fff',
  }
}

);

// 通用文本样式
const commonTextAttrs = {
  fontSize: 12,
  fill: '#333333',
  fontWeight: 500,
}

  ;

const businessTextAttrs = {
  fontSize: 12,
  fill: '#ffffff',
  fontWeight: 500,
}

  ;

// 节点配置哈希表
export const nodeConfigs = {

  //------------------- 基础节点 -------------------
  // // 矩形节点
  // 'flow-rect': {

  //   inherit: 'rect',
  //   width: 120,
  //   height: 60,
  //   attrs: {
  //     body: {
  //       stroke: '#5F95FF',
  //       strokeWidth: 2,
  //       fill: '#EFF7FF',
  //       rx: 8,
  //       ry: 8,
  //     }

  //     ,
  //     text: commonTextAttrs,
  //   }

  //   ,
  //   ports: {
  //     groups: {
  //       top: {
  //         position: 'top',
  //         attrs: createPortAttrs('#5F95FF'),
  //       }

  //       ,
  //       right: {
  //         position: 'right',
  //         attrs: createPortAttrs('#5F95FF'),
  //       }

  //       ,
  //       bottom: {
  //         position: 'bottom',
  //         attrs: createPortAttrs('#5F95FF'),
  //       }

  //       ,
  //       left: {
  //         position: 'left',
  //         attrs: createPortAttrs('#5F95FF'),
  //       }

  //       ,
  //     }

  //     ,
  //     items: [{
  //       group: 'top'
  //     }

  //       ,
  //     {
  //       group: 'right'
  //     }

  //       ,
  //     {
  //       group: 'bottom'
  //     }

  //       ,
  //     {
  //       group: 'left'
  //     }

  //       ,
  //     ],
  //   }

  //   ,
  //   // 用于 stencil 面板
  //   stencil: {
  //     label: '矩形节点',
  //     group: 'basic',
  //   }
  // }

  // ,

  // // 圆形节点
  // 'flow-circle': {

  //   inherit: 'circle',
  //   width: 80,
  //   height: 80,
  //   attrs: {
  //     body: {
  //       stroke: '#5F95FF',
  //       strokeWidth: 2,
  //       fill: '#EFF7FF',
  //     }

  //     ,
  //     text: commonTextAttrs,
  //   }

  //   ,
  //   ports: {
  //     groups: {
  //       top: {
  //         position: 'top',
  //         attrs: createPortAttrs('#5F95FF'),
  //       }

  //       ,
  //       right: {
  //         position: 'right',
  //         attrs: createPortAttrs('#5F95FF'),
  //       }

  //       ,
  //       bottom: {
  //         position: 'bottom',
  //         attrs: createPortAttrs('#5F95FF'),
  //       }

  //       ,
  //       left: {
  //         position: 'left',
  //         attrs: createPortAttrs('#5F95FF'),
  //       }

  //       ,
  //     }

  //     ,
  //     items: [{
  //       group: 'top'
  //     }

  //       ,
  //     {
  //       group: 'right'
  //     }

  //       ,
  //     {
  //       group: 'bottom'
  //     }

  //       ,
  //     {
  //       group: 'left'
  //     }

  //       ,
  //     ],
  //   }

  //   ,
  //   stencil: {
  //     label: '圆形节点',
  //     group: 'basic',
  //   }
  // }

  // ,

  // // 菱形节点
  // 'flow-rhombus': {

  //   inherit: 'polygon',
  //   width: 80,
  //   height: 80,
  //   attrs: {
  //     body: {
  //       stroke: '#5F95FF',
  //       strokeWidth: 2,
  //       fill: '#EFF7FF',
  //       refPoints: '0,0.5 0.5,0 1,0.5 0.5,1',
  //     }

  //     ,
  //     text: commonTextAttrs,
  //   }

  //   ,
  //   ports: {
  //     groups: {
  //       top: {
  //         position: 'top',
  //         attrs: createPortAttrs('#5F95FF'),
  //       }

  //       ,
  //       right: {
  //         position: 'right',
  //         attrs: createPortAttrs('#5F95FF'),
  //       }

  //       ,
  //       left: {
  //         position: 'left',
  //         attrs: createPortAttrs('#5F95FF'),
  //       }

  //       ,
  //       bottom: {
  //         position: 'bottom',
  //         attrs: createPortAttrs('#5F95FF'),
  //       }

  //       ,
  //     }

  //     ,
  //     items: [{
  //       group: 'top'
  //     }

  //       ,
  //     {
  //       group: 'right'
  //     }

  //       ,
  //     {
  //       group: 'bottom'
  //     }

  //       ,
  //     {
  //       group: 'left'
  //     }

  //       ,
  //     ],
  //   }

  //   ,
  //   stencil: {
  //     label: '菱形节点',
  //     group: 'basic',
  //   }
  // }

  // ,


  //------------------- 业务节点 -------------------
  // 开始节点
  'flow-start': {

    inherit: 'circle',
    width: 80,
    height: 80,
    attrs: {
      body: {
        fill: '#3c7eff',
        stroke: '#3c7eff',
        strokeWidth: 1,
      }

      ,
      text: businessTextAttrs,
    }

    ,
    ports: {
      groups: {
        top: {
          position: 'top',
          attrs: createPortAttrs('#1890ff'),
        }

        ,
        right: {
          position: 'right',
          attrs: createPortAttrs('#1890ff'),
        }

        ,
      }

      ,
      items: [{
        group: 'top'
      }

        ,
      {
        group: 'right'
      }

        ,
      ],
    }

    ,
    stencil: {
      label: '开始节点',
      group: 'basic',
    }
  }

  ,

  // 结束节点
  'flow-end': {

    inherit: 'circle',
    width: 80,
    height: 80,
    attrs: {
      body: {
        fill: '#ff120c',
        stroke: '#ff120c',
        strokeWidth: 1,
      }

      ,
      text: businessTextAttrs,
    }

    ,
    ports: {
      groups: {
        top: {
          position: {

            name: 'absolute',
            args: {
              x: 0.5, y: 0
            }
          }

          ,
          attrs: createPortAttrs('#FF5F5F'),
        }

        ,
        left: {
          position: {

            name: 'absolute',
            args: {
              x: 0, y: 0.5
            }
          }

          ,
          attrs: createPortAttrs('#FF5F5F'),
        }

        ,
      }

      ,
      items: [{
        group: 'top'
      }

        ,
      {
        group: 'left'
      }

        ,
      ],
    }

    ,
    stencil: {
      label: '结束节点',
      group: 'basic',
    }
  }

  ,

  // 处理节点
  'flow-process': {

    inherit: 'rect',
    width: 120,
    height: 60,
    attrs: {
      body: {
        fill: '#6e6e6e',
        stroke: '#6e6e6e',
        strokeWidth: 1,
        rx: 8,
        ry: 8,
      }

      ,
      text: businessTextAttrs,
    }

    ,
    ports: {
      groups: {
        top: {
          position: 'top',
          attrs: createPortAttrs('#6e6e6e'),
        }

        ,
        right: {
          position: 'right',
          attrs: createPortAttrs('#6e6e6e'),
        }

        ,
        left: {
          position: 'left',
          attrs: createPortAttrs('#6e6e6e'),
        }

        ,
        bottom: {
          position: 'bottom',
          attrs: createPortAttrs('#6e6e6e'),
        }

        ,
      }

      ,
      items: [{
        group: 'top'
      }

        ,
      {
        group: 'right'
      }

        ,
      {
        group: 'left'
      }

        ,
      {
        group: 'bottom'
      }

        ,
      ],
    }

    ,
    stencil: {
      label: '处理节点',
      group: 'basic',
    }
  }

  ,

  // 判断节点
  'flow-decision': {

    inherit: 'polygon',
    width: 90,
    height: 80,
    attrs: {
      body: {
        fill: '#f7ba1e',
        stroke: '#f7ba1e',
        strokeWidth: 1,
        refPoints: '10,0 20,10 10,20 0,10',
      }

      ,
      text: businessTextAttrs,
    }

    ,
    ports: {
      groups: {
        top: {
          position: 'top',
          attrs: createPortAttrs('#f7ba1e'),
        }

        ,
        right: {
          position: 'right',
          attrs: createPortAttrs('#f7ba1e'),
        }

        ,
        bottom: {
          position: 'bottom',
          attrs: createPortAttrs('#f7ba1e'),
        }

        ,
        left: {
          position: 'left',
          attrs: createPortAttrs('#f7ba1e'),
        }

        ,
      }

      ,
      items: [{
        group: 'top'
      }

        ,
      {
        group: 'right'
      }

        ,
      {
        group: 'bottom'
      }

        ,
      {
        group: 'left'
      }

        ,
      ],
    }

    ,
    stencil: {
      label: '判断节点',
      group: 'basic',
    }
  }

  ,
}

  ;