{"name": "vue-flow", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --open", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@antv/x6": "^2.18.1", "@antv/x6-plugin-clipboard": "^2.1.6", "@antv/x6-plugin-export": "^2.1.6", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-keyboard": "^2.2.3", "@antv/x6-plugin-minimap": "^2.0.7", "@antv/x6-plugin-scroller": "^2.0.10", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-plugin-snapline": "^2.1.7", "@antv/x6-plugin-stencil": "^2.1.5", "@antv/x6-plugin-transform": "^2.1.8", "@antv/x6-vue-shape": "^2.1.2", "ant-design-vue": "^3.2.0", "axios": "^1.9.0", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^4.5.10"}}