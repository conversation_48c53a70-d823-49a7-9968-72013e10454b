# Vue Flow - 基于 AntV X6 的流程图编辑器

Vue Flow 是一个基于 Vue 3 和 AntV X6 构建的强大流程图编辑器，提供了丰富的功能和直观的用户界面，适用于各种流程设计、工作流管理和图形化编辑场景。

## 安装与运行

### 前提条件

- Node.js 16.0 或更高版本
- npm 或 yarn

### 安装依赖

```bash
# 使用 npm
npm install

# 或使用 yarn
yarn
```

### 开发模式运行

```bash
# 使用 npm
npm run dev

# 或使用 yarn
yarn dev
```

### 构建生产版本

```bash
# 使用 npm
npm run build

# 或使用 yarn
yarn build
```

### 预览生产构建

```bash
# 使用 npm
npm run preview

# 或使用 yarn
yarn preview
```

## 项目结构

```
src/
├── assets/           # 静态资源
├── components/       # 组件
│   ├── edges/        # 边缘线相关组件
│   ├── flow/         # 流程图相关组件
│   └── nodes/        # 节点相关组件
├── composables/      # 组合式函数
├── constants/        # 常量定义
├── utils/            # 工具函数
├── views/            # 页面视图
├── App.vue           # 应用入口组件
├── main.js           # 应用入口文件
└── style.css         # 全局样式
```

## 核心功能

### 流程图编辑

- 从侧边栏拖拽节点到画布
- 通过连接桩创建连线
- 调整节点大小和位置
- 编辑节点和连线的文本和样式

### 节点配置

- 修改节点文本
- 调整节点大小、颜色和边框样式
- 设置节点圆角

### 连线配置

- 修改连线类型（直线、曲线、折线等）
- 调整连线颜色、宽度和样式
- 设置连线文本和箭头样式

### 画布操作

- 缩放和平移画布
- 使用小地图导航
- 居中显示内容

### 导入/导出

- 导出流程图为 JSON 格式
- 导入 JSON 格式的流程图数据

## 使用示例

### 创建流程图

1. 从左侧节点库拖拽节点到画布
2. 通过节点上的连接桩创建连线
3. 双击节点或连线编辑文本
4. 选中节点或连线，在右侧面板配置样式

### 导出流程图

1. 点击工具栏中的导出按钮
2. 选择导出为 JSON 格式
3. 保存导出的文件

### 导入流程图

1. 点击工具栏中的导入按钮
2. 选择之前导出的 JSON 文件
3. 流程图将被加载到画布中

